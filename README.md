# Digital Heist Investigation - CTF Challenge

## Challenge Description

**Difficulty:** Medium  
**Category:** Digital Forensics / Malware Analysis  
**Flag Format:** `TRIADA{...}`

### Scenario

Your company's security team has detected a sophisticated cyber attack on one of the corporate workstations. The incident response team has collected several forensic artifacts from the compromised system:

- A suspicious PowerShell script (`malicious.ps1`)
- A memory dump from the affected system (`system_memory.dmp`)
- System event logs (`system_events.log`)
- Network traffic capture (`network_capture.pcap.txt`)
- Registry export from the compromised machine (`registry_export.reg`)

The attackers appear to have used multiple layers of obfuscation and anti-analysis techniques to hide their activities. Your task is to analyze these artifacts and reconstruct the complete attack chain to find the hidden flag.

### Objectives

1. **Analyze the obfuscated PowerShell script** - The script contains multiple layers of encoding and obfuscation
2. **Examine the memory dump** - Look for artifacts left behind by the malicious process
3. **Correlate network traffic** - Identify command and control communications
4. **Review system logs** - Find evidence of the attack timeline
5. **Investigate registry modifications** - Discover persistence mechanisms

### Files Provided

- `malicious.ps1` - Obfuscated PowerShell script found on the system
- `system_memory.dmp` - Memory dump captured during the incident
- `system_events.log` - System event logs from the time of compromise
- `network_capture.pcap.txt` - Network traffic analysis (text export)
- `registry_export.reg` - Registry export containing suspicious entries

### Hints

1. The flag is split across multiple artifacts - you'll need to analyze all of them
2. Pay attention to different encoding schemes: Base64, XOR, ROT13, and hex
3. Some data may be hidden in plain sight within log timestamps or registry keys
4. The PowerShell script contains the key to decoding other artifacts
5. Network traffic may contain additional flag components
6. Look for patterns in the data that might indicate encoding or obfuscation

### Tools You Might Need

- Text editor or hex editor
- Base64 decoder
- PowerShell (for script analysis)
- Python (for custom decoding scripts)
- Strings utility (for memory dump analysis)
- Calculator (for XOR operations)

### Submission

Submit the complete flag in the format: `TRIADA{...}`

### Difficulty Breakdown

- **Beginner:** Basic Base64 decoding and string analysis
- **Intermediate:** Multi-layer obfuscation, XOR decryption, pattern recognition
- **Advanced:** Memory dump analysis, correlation across multiple artifacts

Good luck, and remember - the attackers were clever, but not clever enough to hide from a thorough forensic investigation!

---

*This challenge is designed to simulate real-world incident response scenarios. All artifacts are synthetic and created for educational purposes.*
