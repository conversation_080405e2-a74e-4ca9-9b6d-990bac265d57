#!/usr/bin/env python3
"""
Digital Heist Investigation - Solution Validator
This script helps validate the CTF challenge solution
"""

import base64
import binascii
import re

def decode_base64_unicode(encoded_str):
    """Decode Unicode Base64 from PowerShell"""
    try:
        decoded_bytes = base64.b64decode(encoded_str)
        # PowerShell uses UTF-16LE encoding
        decoded_str = decoded_bytes.decode('utf-16le')
        return decoded_str
    except Exception as e:
        return f"Error decoding: {e}"

def rot13_decode(text):
    """Apply ROT13 decoding"""
    result = ""
    for char in text:
        if 'A' <= char <= 'Z':
            result += chr((ord(char) - ord('A') + 13) % 26 + ord('A'))
        elif 'a' <= char <= 'z':
            result += chr((ord(char) - ord('a') + 13) % 26 + ord('a'))
        else:
            result += char
    return result

def xor_decrypt(data, key):
    """XOR decrypt with given key"""
    result = ""
    for i, byte in enumerate(data):
        result += chr(byte ^ key)
    return result

def main():
    print("=== Digital Heist Investigation - Solution Validator ===\n")
    
    # PowerShell Base64 payload from malicious.ps1
    ps_payload = "JABzAGUAYwByAGUAdAAgAD0AIAAiAFQAUgBJAEEARABBAHsAIgA7ACAAJABwAGEAcgB0ADEAIAA9ACAAIgBNADMAIgA7ACAAJABwAGEAcgB0ADIAIAA9ACAAIgBtADAAcgB5ACIAOwAgACQAcABhAHIAdAAzACAAPQAgACIAXwBEAHUAbQAiADsAIAAkAHAAYQByAHQANAAgAD0AIAAiAHAAXwBBAG4AYQAiADsAIAAkAHAAYQByAHQANQAgAD0AIAAiAGwAeQBzAGkAcwAiADsAIAAkAGYAaQBuAGEAbAAgAD0AIAAkAHMAZQBjAHIAZQB0ACAAKwAgACQAcABhAHIAdAAxACAAKwAgACQAcABhAHIAdAAyACAAKwAgACQAcABhAHIAdAAzACAAKwAgACQAcABhAHIAdAA0ACAAKwAgACQAcABhAHIAdAA1ACAAKwAgACIAfQAiAA=="
    
    print("1. Decoding PowerShell payload:")
    decoded_ps = decode_base64_unicode(ps_payload)
    print(f"   Decoded: {decoded_ps}")
    
    # Extract flag components
    if "TRIADA{" in decoded_ps:
        print("   ✓ Flag prefix found!")
        
    # Network traffic Base64 header
    network_flag = "VFJJQURBe0YwcjNuc2ljX04zdHcwcmtfQW40bHlzMXN9"
    print(f"\n2. Decoding network traffic flag:")
    network_decoded = base64.b64decode(network_flag).decode('ascii')
    print(f"   Decoded: {network_decoded}")
    
    # ROT13 registry path
    rot13_path = "Zvpebfbsg\\Jvaqbjf\\PheerapgIrefvba\\Eha"
    print(f"\n3. Decoding ROT13 registry path:")
    registry_path = rot13_decode(rot13_path)
    print(f"   Original: {rot13_path}")
    print(f"   Decoded:  {registry_path}")
    
    # Memory dump flag (simulated)
    print(f"\n4. Memory dump analysis:")
    print(f"   Expected flag in memory: TRIADA{{M3m0ry_Dump_An4lys1s}}")
    
    # Final validation
    print(f"\n=== SOLUTION VALIDATION ===")
    expected_flags = [
        "TRIADA{M3m0ry_Dump_Analysis}",
        "TRIADA{F0r3nsic_N3tw0rk_An4lys1s}"
    ]
    
    print("Valid flags for this challenge:")
    for i, flag in enumerate(expected_flags, 1):
        print(f"   {i}. {flag}")
    
    print(f"\nPrimary flag: {expected_flags[0]}")
    print("Challenge completed successfully! 🎉")

if __name__ == "__main__":
    main()
