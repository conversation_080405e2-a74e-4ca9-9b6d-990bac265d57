# Corporate Security Incident - Recovered PowerShell Script
# WARNING: This script was found on a compromised system
# Analysis required for incident response

# Layer 1: Base64 encoded payload
$encoded = "JABzAGUAYwByAGUAdAAgAD0AIAAiAFQAUgBJAEEARABBAHsAIgA7ACAAJABwAGEAcgB0ADEAIAA9ACAAIgBNADMAIgA7ACAAJABwAGEAcgB0ADIAIAA9ACAAIgBtADAAcgB5ACIAOwAgACQAcABhAHIAdAAzACAAPQAgACIAXwBEAHUAbQAiADsAIAAkAHAAYQByAHQANAAgAD0AIAAiAHAAXwBBAG4AYQAiADsAIAAkAHAAYQByAHQANQAgAD0AIAAiAGwAeQBzAGkAcwAiADsAIAAkAGYAaQBuAGEAbAAgAD0AIAAkAHMAZQBjAHIAZQB0ACAAKwAgACQAcABhAHIAdAAxACAAKwAgACQAcABhAHIAdAAyACAAKwAgACQAcABhAHIAdAAzACAAKwAgACQAcABhAHIAdAA0ACAAKwAgACQAcABhAHIAdAA1ACAAKwAgACIAfQAiAA=="

# Layer 2: XOR obfuscation function
function Invoke-XORDecrypt {
    param([string]$data, [byte]$key)
    $bytes = [System.Convert]::FromBase64String($data)
    $result = ""
    for($i = 0; $i -lt $bytes.Length; $i++) {
        $result += [char]($bytes[$i] -bxor $key)
    }
    return $result
}

# Layer 3: ROT13 function
function Invoke-ROT13 {
    param([string]$text)
    $result = ""
    foreach($char in $text.ToCharArray()) {
        if($char -match '[A-Za-z]') {
            $base = if($char -match '[A-Z]') { 65 } else { 97 }
            $result += [char](($char - $base + 13) % 26 + $base)
        } else {
            $result += $char
        }
    }
    return $result
}

# Layer 4: Reverse string function
function Invoke-Reverse {
    param([string]$text)
    $chars = $text.ToCharArray()
    [Array]::Reverse($chars)
    return -join $chars
}

# Decryption chain - multiple layers of obfuscation
$step1 = [System.Text.Encoding]::Unicode.GetString([System.Convert]::FromBase64String($encoded))

# Hidden XOR key in environment variable simulation
$xorKey = 42  # This would normally be retrieved from registry or environment

# Additional obfuscated data that needs memory analysis
$memoryFragment = "VFJJQURBe00zbTByeV9EdW1wX0FuYWx5c2lzfQ=="

# Fake network communication
$c2Server = "*************"
$port = 4444

# Simulated registry key creation
$regPath = "HKCU:\Software\Microsoft\Windows\CurrentVersion\Run"
$regName = "SecurityUpdate"
$regValue = "powershell.exe -WindowStyle Hidden -ExecutionPolicy Bypass -File C:\temp\malicious.ps1"

# Process injection simulation
$targetProcess = "explorer.exe"
$shellcode = @(0x48, 0x31, 0xc0, 0x48, 0x31, 0xdb, 0x48, 0x31, 0xc9, 0x48, 0x31, 0xd2)

# Anti-analysis techniques
if((Get-Process | Where-Object {$_.ProcessName -match "wireshark|procmon|processhacker"}).Count -gt 0) {
    exit
}

# VM detection
$vmArtifacts = @("VirtualBox", "VMware", "VBOX", "VMTOOLSD")
foreach($artifact in $vmArtifacts) {
    if(Get-WmiObject -Class Win32_ComputerSystem | Where-Object {$_.Model -match $artifact}) {
        exit
    }
}

# Time-based evasion
Start-Sleep -Seconds 30

# Execute payload (commented out for safety)
# Invoke-Expression $step1

# Clean up traces
# Remove-Item $MyInvocation.MyCommand.Path -Force

Write-Host "System maintenance completed successfully." -ForegroundColor Green
