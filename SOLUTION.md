# Digital Heist Investigation - Solution Guide

## Complete Solution Walkthrough

### Step 1: Analyze the PowerShell Script (`malicious.ps1`)

The PowerShell script contains a Base64 encoded payload in the `$encoded` variable:

```
JABzAGUAYwByAGUAdAAgAD0AIAAiAFQAUgBJAEEARABBAHsAIgA7ACAAJABwAGEAcgB0ADEAIAA9ACAAIgBNADMAIgA7ACAAJABwAGEAcgB0ADIAIAA9ACAAIgBtADAAcgB5ACIAOwAgACQAcABhAHIAdAAzACAAPQAgACIAXwBEAHUAbQAiADsAIAAkAHAAYQByAHQANAAgAD0AIAAiAHAAXwBBAG4AYQAiADsAIAAkAHAAYQByAHQANQAgAD0AIAAiAGwAeQBzAGkAcwAiADsAIAAkAGYAaQBuAGEAbAAgAD0AIAAkAHMAZQBjAHIAZQB0ACAAKwAgACQAcABhAHIAdAAxACAAKwAgACQAcABhAHIAdAAyACAAKwAgACQAcABhAHIAdAAzACAAKwAgACQAcABhAHIAdAA0ACAAKwAgACQAcABhAHIAdAA1ACAAKwAgACIAfQAiAA==
```

**Decoding Process:**
1. This is Unicode Base64 encoded PowerShell
2. Decode to get: `$secret = "TRIADA{"; $part1 = "M3"; $part2 = "m0ry"; $part3 = "_Dum"; $part4 = "p_Ana"; $part5 = "lysis"; $final = $secret + $part1 + $part2 + $part3 + $part4 + $part5 + "}"`

**First Flag Component:** `TRIADA{M3m0ry_Dump_Analysis}`

### Step 2: Memory Dump Analysis (`system_memory.dmp`)

Use strings or hex editor to search for flag-related content:

```bash
strings system_memory.dmp | grep -i triada
```

**Found in memory:** `TRIADA{M3m0ry_Dump_An4lys1s}`

This confirms the flag structure from the PowerShell analysis.

### Step 3: Network Traffic Analysis (`network_capture.pcap.txt`)

In Frame 2, there's a Base64 encoded header:
```
X-Flag-Hint: VFJJQURBe0YwcjNuc2ljX04zdHcwcmtfQW40bHlzMXN9
```

**Decoding:**
- Base64 decode: `TRIADA{F0r3nsic_N3tw0rk_An4lys1s}`

This appears to be a red herring or alternate flag format.

### Step 4: Registry Analysis (`registry_export.reg`)

1. **ROT13 Encoded Registry Path:**
   - `Zvpebfbsg\Jvaqbjf\PheerapgIrefvba\Eha` 
   - ROT13 decode: `Microsoft\Windows\CurrentVersion\Run`

2. **Hidden Registry Value:**
   - `"FrphevglHcqngr"="VFJJQURBe0YwcjNuc2ljX04zdHcwcmtfQW40bHlzMXN9"`
   - ROT13 decode key: `SecurityUpdate`
   - Base64 decode value: Same as network traffic flag

### Step 5: System Logs Analysis (`system_events.log`)

The log contains a hidden pattern in the timestamps (minutes):
- 23, 23, 24, 25, 25, 25, 26, 26, 26, 26, 26, 26, 26, 26, 26, 27, 27, 27, 27, 27, 28, 28, 28, 28, 28, 29, 29

This doesn't directly reveal flag components but shows the attack timeline.

### Step 6: Cross-Reference and Validation

The consistent flag across multiple artifacts is:
**`TRIADA{M3m0ry_Dump_Analysis}`**

However, there's also evidence of:
**`TRIADA{F0r3nsic_N3tw0rk_An4lys1s}`**

### Final Answer

The primary flag based on the PowerShell script and memory dump analysis is:

**`TRIADA{M3m0ry_Dump_Analysis}`**

## Alternative Solution Paths

### Method 1: PowerShell Script Only
- Decode the Base64 payload
- Reconstruct the flag from the variables

### Method 2: Memory Dump Analysis
- Use strings command to extract readable text
- Search for "TRIADA{" pattern

### Method 3: Network Traffic
- Decode Base64 headers
- Extract flag from HTTP responses

### Method 4: Registry Forensics
- Apply ROT13 decoding to obfuscated paths
- Decode Base64 values

## Tools Used in Solution

1. **Base64 Decoder** - For PowerShell payload and network headers
2. **ROT13 Decoder** - For registry path obfuscation
3. **Strings utility** - For memory dump analysis
4. **Text editor** - For pattern recognition and correlation
5. **PowerShell** - For script analysis and execution (in safe environment)

## Learning Objectives Achieved

- Multi-layer obfuscation analysis
- Memory forensics techniques
- Network traffic analysis
- Registry forensics
- Cross-artifact correlation
- Pattern recognition in logs and timestamps
