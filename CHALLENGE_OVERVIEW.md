# Digital Heist Investigation - CTF Challenge Overview

## Challenge Summary

**Title:** Digital Heist Investigation  
**Difficulty:** Medium  
**Category:** Digital Forensics / Malware Analysis  
**Flag Format:** `TRIADA{...}`  
**Primary Flag:** `TRIADA{M3m0ry_Dump_Analysis}`

## Files Created

### Challenge Files (Provided to Participants)
1. **`README.md`** - Challenge description and instructions
2. **`malicious.ps1`** - Obfuscated PowerShell script with multi-layer encoding
3. **`system_memory.dmp`** - Simulated memory dump with embedded artifacts
4. **`system_events.log`** - System event logs with attack timeline
5. **`network_capture.pcap.txt`** - Network traffic analysis (text export)
6. **`registry_export.reg`** - Registry export with persistence mechanisms

### Solution Files (For Organizers)
7. **`SOLUTION.md`** - Complete solution walkthrough
8. **`validate_solution.py`** - Python script to validate solutions
9. **`CHALLENGE_OVERVIEW.md`** - This overview file

## Challenge Components

### 1. Multi-Layer Obfuscation
- **Base64 encoding** (Unicode PowerShell format)
- **XOR encryption** with key 42
- **ROT13 encoding** for registry paths
- **Hex encoding** in network traffic

### 2. Forensic Artifacts
- **Memory dump** with process artifacts and flag fragments
- **Network traffic** showing C2 communications
- **Registry modifications** for persistence
- **System logs** with attack timeline
- **PowerShell script** with anti-analysis techniques

### 3. Skills Tested
- Static malware analysis
- Memory forensics
- Network traffic analysis
- Registry forensics
- Multi-artifact correlation
- Pattern recognition
- Encoding/decoding techniques

## Solution Paths

### Primary Path (Recommended)
1. Analyze PowerShell script → Decode Base64 payload
2. Extract flag components from decoded variables
3. Validate with memory dump analysis
4. Cross-reference with other artifacts

### Alternative Paths
- Memory dump strings analysis
- Network traffic header decoding
- Registry value decoding
- Log pattern analysis

## Difficulty Breakdown

- **Beginner (30%):** Basic Base64 decoding
- **Intermediate (50%):** Multi-layer obfuscation, correlation
- **Advanced (20%):** Memory analysis, pattern recognition

## Educational Value

This challenge simulates real-world incident response scenarios including:
- Malware analysis techniques
- Digital forensics workflows
- Multi-source evidence correlation
- Common obfuscation methods used by attackers
- Persistence mechanisms in Windows environments

## Setup Instructions

1. Provide participants with challenge files (items 1-6)
2. Ensure they have access to basic forensic tools
3. Allow 2-3 hours for completion
4. Use validation script to check submissions

## Flag Validation

**Primary Flag:** `TRIADA{M3m0ry_Dump_Analysis}`  
**Alternative Flag:** `TRIADA{F0r3nsic_N3tw0rk_An4lys1s}`

Both flags are valid as they can be derived from different analysis paths, but the primary flag is the intended solution based on the PowerShell script analysis.

---

*Challenge created for educational purposes. All artifacts are synthetic and safe for analysis.*
